"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import type { IndexItem } from "../types";
import IndicesTable from "./IndicesTable";
import RangeSelector from "./RangeSelector";
import LineChart from "./LineChart";
import StatusHint from "./StatusHint";
import useQuoteData from "./useQuoteData";
import GainCalculator from "./GainCalculator";
import useGainCalculation from "./useGainCalculation";
import { AnimatePresence, motion } from "framer-motion";



type Range = "3m" | "6m" | "1y";

export default function IndicesClient({
  indices,
}: {
  indices: IndexItem[];
}) {
  const { data: session, status } = useSession();
  const router = useRouter();


  // 当前选中指数
  const [activeSymbol, setActiveSymbol] = useState<string | null>(
    Array.isArray(indices) && indices.length > 0 ? indices[0].symbol : null
  );
  // 选中区间
  const [selectedRange, setSelectedRange] = useState<Range>("3m");

  // 搜索关键词
  const [searchTerm, setSearchTerm] = useState<string>("");

  // 排序字段与顺序
  const [sortField, setSortField] = useState<"latest_close" | "latest_change" | "latest_change_percent">("latest_close");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // 登录校验
  useEffect(() => {
    if (process.env.NODE_ENV === "development") return;
    if (status === "loading") return;
    if (!session) {
      router.replace("/auth/signin");
    }
  }, [session, status, router]);

  // 行情数据请求
  const {
    data: quoteList,
    loading: isQuoteLoading,
    error: isQuoteError,
    retry: retryQuote,
    retryLoading: isRetryLoading
  } = useQuoteData(
    activeSymbol,
    selectedRange
  );

  // 涨幅计算数据管理
  const {
    indicesQuoteData,
    loading: gainDataLoading,
    error: gainDataError,
    loadHistoryData,
  } = useGainCalculation(indices);

  // 处理涨幅计算错误
  useEffect(() => {
    if (gainDataError) {
      console.error('Failed to load gain calculation data');
    }
  }, [gainDataError]);

  // indices 为空兜底处理
  if (!Array.isArray(indices) || indices.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <StatusHint empty emptyText="暂无指数数据" />
      </div>
    );
  }

  // 页面加载状态
  if (status === "loading") {
    return <StatusHint loading loadingText="加载中..." />;
  }

  // 搜索过滤
  const filteredIndices = indices.filter(idx =>
    idx.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    idx.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 排序
  const sortedIndices = [...filteredIndices].sort((a, b) => {
    let vA = a[sortField];
    let vB = b[sortField];
    if (typeof vA === "string") vA = parseFloat(vA);
    if (typeof vB === "string") vB = parseFloat(vB);
    if (sortOrder === "asc") {
      return vA > vB ? 1 : vA < vB ? -1 : 0;
    } else {
      return vA < vB ? 1 : vA > vB ? -1 : 0;
    }
  });

  // 排序切换回调
  const handleSort = (field: typeof sortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("desc");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      {/* 优化的页面标题 */}
      <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                A股主要宽基指数
              </h1>
              <p className="mt-1 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                实时监控主要股票指数走势，点击指数查看详细图表
              </p>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>实时数据</span>
            </div>
          </div>
        </div>
      </div>

      {/* 优化的主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
        {/* 涨幅计算器 */}
        <div className="mb-6 lg:mb-8">
          <GainCalculator
            indices={indices}
            indicesQuoteData={indicesQuoteData}
            onLoadHistoryData={loadHistoryData}
            loading={gainDataLoading}
            className="w-full"
          />
        </div>

        <div className="flex flex-col xl:flex-row gap-6 lg:gap-8">

          {/* 优化的左侧指数列表 */}
          <div className="w-full xl:w-2/5 flex-shrink-0">
            <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
              <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <div>
                    <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">
                      指数列表
                    </h2>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      共 {filteredIndices.length} 个指数
                    </p>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    <span>实时行情</span>
                  </div>
                </div>

                {/* 优化的搜索框 */}
                <div className="mt-4 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white/80 dark:bg-gray-900/80 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-base"
                    placeholder="搜索指数名称或代码..."
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                  />
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm("")}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>

              <div className="max-h-[600px] lg:max-h-[700px] overflow-y-auto overflow-x-auto">
                {/* 横向滚动容器，移动端可滑动 */}
                <div className="min-w-[420px] sm:min-w-0">
                  <IndicesTable
                    indices={sortedIndices}
                    activeSymbol={activeSymbol}
                    onSelect={setActiveSymbol}
                    sortField={sortField}
                    sortOrder={sortOrder}
                    onSort={handleSort}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 优化的右侧图表区域 */}
          <div className="w-full xl:w-3/5 flex-grow">
            <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">

              {/* 优化的图表头部 */}
              <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-700">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3">
                      <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">
                        {activeSymbol ? `${activeSymbol} 走势图` : '请选择指数'}
                      </h2>
                      {activeSymbol && (
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">实时</span>
                        </div>
                      )}
                    </div>
                    {activeSymbol && (
                      <div className="mt-2 flex flex-col sm:flex-row sm:items-center gap-2">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {indices.find(idx => idx.symbol === activeSymbol)?.name || activeSymbol}
                        </p>
                        {(() => {
                          const currentIndex = indices.find(idx => idx.symbol === activeSymbol);
                          if (currentIndex) {
                            const isPositive = currentIndex.latest_change >= 0;
                            return (
                              <div className="flex items-center gap-3 text-sm">
                                <span className="font-medium text-gray-900 dark:text-gray-100">
                                  {currentIndex.latest_close}
                                </span>
                                <span className={`font-medium ${isPositive ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                                  {isPositive ? '+' : ''}{currentIndex.latest_change} ({isPositive ? '+' : ''}{currentIndex.latest_change_percent}%)
                                </span>
                              </div>
                            );
                          }
                          return null;
                        })()}
                      </div>
                    )}
                  </div>
                  <div className="flex-shrink-0">
                    <RangeSelector
                      selected={selectedRange}
                      loading={isQuoteLoading}
                      onChange={setSelectedRange}
                    />
                  </div>
                </div>
              </div>

              {/* 优化的图表内容 */}
              <div className="p-4 sm:p-6">
                <div className="h-[400px] sm:h-[500px] lg:h-[600px] flex items-center justify-center rounded-xl bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 border border-gray-200/50 dark:border-gray-700/50 shadow-inner">
                  {!activeSymbol ? (
                    <div className="text-center py-12">
                      <div className="text-gray-400 dark:text-gray-500 mb-6">
                        <svg className="w-20 h-20 mx-auto opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                        选择指数查看走势
                      </h3>
                      <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                        从左侧列表中选择一个指数，查看其详细的价格走势图表和技术指标
                      </p>
                      <div className="mt-6 flex justify-center">
                        <div className="flex items-center gap-2 text-sm text-gray-400 dark:text-gray-500">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                          </svg>
                          <span>点击左侧指数开始</span>
                        </div>
                      </div>
                    </div>
                  ) : isQuoteLoading ? (
                    <div className="w-full flex flex-col items-center gap-6 py-12">
                      <div className="flex items-center gap-3">
                        <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-gray-600 dark:text-gray-400">加载图表数据中...</span>
                      </div>
                      <div className="w-full space-y-4 animate-pulse">
                        <div className="w-3/4 h-6 bg-gray-200 dark:bg-gray-700 rounded mx-auto"></div>
                        <div className="w-full h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="flex justify-center gap-4">
                          <div className="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          <div className="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          <div className="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        </div>
                      </div>
                    </div>
                  ) : isQuoteError ? (
                    <div className="py-12">
                      <StatusHint
                        error={true}
                        errorText="数据加载失败，请稍后重试"
                        onRetry={retryQuote}
                        retryLoading={isRetryLoading}
                      />
                    </div>
                  ) : quoteList.length === 0 ? (
                    <div className="py-12">
                      <StatusHint
                        empty={true}
                        emptyText="暂无图表数据"
                      />
                    </div>
                  ) : (
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={activeSymbol}
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -20, scale: 0.95 }}
                        transition={{ duration: 0.4, ease: "easeOut" }}
                        className="w-full h-full"
                      >
                        <LineChart data={quoteList} />
                      </motion.div>
                    </AnimatePresence>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}