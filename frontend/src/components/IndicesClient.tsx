"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import type { IndexItem } from "../types";
import IndicesTable from "./IndicesTable";
import RangeSelector from "./RangeSelector";
import LineChart from "./LineChart";
import StatusHint from "./StatusHint";
import useQuoteData from "./useQuoteData";
import { AnimatePresence, motion } from "framer-motion";



type Range = "3m" | "6m" | "1y";

export default function IndicesClient({
  indices,
}: {
  indices: IndexItem[];
}) {
  const { data: session, status } = useSession();
  const router = useRouter();


  // 当前选中指数
  const [activeSymbol, setActiveSymbol] = useState<string | null>(
    Array.isArray(indices) && indices.length > 0 ? indices[0].symbol : null
  );
  // 选中区间
  const [selectedRange, setSelectedRange] = useState<Range>("3m");

  // 搜索关键词
  const [searchTerm, setSearchTerm] = useState<string>("");

  // 排序字段与顺序
  const [sortField, setSortField] = useState<"latest_close" | "latest_change" | "latest_change_percent">("latest_close");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // 登录校验
  useEffect(() => {
    if (process.env.NODE_ENV === "development") return;
    if (status === "loading") return;
    if (!session) {
      router.replace("/auth/signin");
    }
  }, [session, status, router]);

  // 行情数据请求
  const {
    data: quoteList,
    loading: isQuoteLoading,
    error: isQuoteError,
    retry: retryQuote,
    retryLoading: isRetryLoading
  } = useQuoteData(
    activeSymbol,
    selectedRange
  );

  // indices 为空兜底处理
  if (!Array.isArray(indices) || indices.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <StatusHint empty emptyText="暂无指数数据" />
      </div>
    );
  }

  // 页面加载状态
  if (status === "loading") {
    return <StatusHint loading loadingText="加载中..." />;
  }

  // 搜索过滤
  const filteredIndices = indices.filter(idx =>
    idx.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    idx.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 排序
  const sortedIndices = [...filteredIndices].sort((a, b) => {
    let vA = a[sortField];
    let vB = b[sortField];
    if (typeof vA === "string") vA = parseFloat(vA);
    if (typeof vB === "string") vB = parseFloat(vB);
    if (sortOrder === "asc") {
      return vA > vB ? 1 : vA < vB ? -1 : 0;
    } else {
      return vA < vB ? 1 : vA > vB ? -1 : 0;
    }
  });

  // 排序切换回调
  const handleSort = (field: typeof sortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("desc");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 页面标题 */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            A股主要宽基指数
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            实时监控主要股票指数走势，点击指数查看详细图表
          </p>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">

          {/* 左侧指数列表 */}
          <div className="w-full lg:w-2/5 flex-shrink-0">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  指数列表
                </h2>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  共 {filteredIndices.length} 个指数
                </p>
                <input
                  type="text"
                  className="mt-2 w-full px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500
                  text-base sm:text-sm
                  sm:py-2 py-3
                  "
                  style={{ fontSize: "16px" }}
                  placeholder="搜索名称或代码…"
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="max-h-[600px] overflow-y-auto overflow-x-auto">
                {/* 横向滚动容器，移动端可滑动 */}
                <div className="min-w-[420px] sm:min-w-0">
                  <IndicesTable
                    indices={sortedIndices}
                    activeSymbol={activeSymbol}
                    onSelect={setActiveSymbol}
                    sortField={sortField}
                    sortOrder={sortOrder}
                    onSort={handleSort}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 右侧图表区域 */}
          <div className="w-full lg:w-3/5 flex-grow">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">

              {/* 图表头部 */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {activeSymbol ? `${activeSymbol} 走势图` : '请选择指数'}
                    </h2>
                    {activeSymbol && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {indices.find(idx => idx.symbol === activeSymbol)?.name || activeSymbol}
                      </p>
                    )}
                  </div>
                  <RangeSelector
                    selected={selectedRange}
                    loading={isQuoteLoading}
                    onChange={setSelectedRange}
                  />
                </div>
              </div>

              {/* 图表内容 */}
              <div className="p-6">
                <div className="h-[500px] flex items-center justify-center rounded-lg bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
                  {!activeSymbol ? (
                    <div className="text-center">
                      <div className="text-gray-400 dark:text-gray-500 mb-2">
                        <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                      <p className="text-gray-500 dark:text-gray-400">
                        请从左侧列表选择一个指数查看走势图
                      </p>
                    </div>
                  ) : isQuoteLoading ? (
                    <div className="w-full flex flex-col items-center gap-4 animate-pulse">
                      <div className="w-3/4 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      <div className="w-full h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      <div className="flex gap-2">
                        <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                    </div>
                  ) : isQuoteError ? (
                    <StatusHint
                      error={true}
                      errorText="数据加载失败，请稍后重试"
                      onRetry={retryQuote}
                      retryLoading={isRetryLoading}
                    />
                  ) : quoteList.length === 0 ? (
                    <StatusHint
                      empty={true}
                      emptyText="暂无数据"
                    />
                  ) : (
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={activeSymbol}
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -30 }}
                        transition={{ duration: 0.35, ease: "easeInOut" }}
                        className="w-full h-full"
                      >
                        <LineChart data={quoteList} />
                      </motion.div>
                    </AnimatePresence>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}