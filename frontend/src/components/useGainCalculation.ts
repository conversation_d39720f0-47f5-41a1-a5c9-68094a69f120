import { useEffect, useState, useCallback } from "react";
import { getBatchIndexHistoryData } from "../services/api";
import type { IndexItem, QuoteItem } from "../types";
import { getSession } from "next-auth/react";

interface UseGainCalculationResult {
  indicesQuoteData: { [symbol: string]: QuoteItem[] };
  loading: boolean;
  error: boolean;
  loadHistoryData: (startDate: string, endDate?: string) => Promise<void>;
  clearData: () => void;
}

/**
 * 涨幅计算数据管理Hook
 */
export function useGainCalculation(indices: IndexItem[]): UseGainCalculationResult {
  const [indicesQuoteData, setIndicesQuoteData] = useState<{ [symbol: string]: QuoteItem[] }>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);

  // 加载历史数据
  const loadHistoryData = useCallback(async (startDate: string, endDate?: string) => {
    if (!indices || indices.length === 0) {
      return;
    }

    setLoading(true);
    setError(false);

    try {
      // 获取token
      let token = "";
      if (process.env.NODE_ENV !== 'development') {
        const session = await getSession();
        token = (session as any)?.accessToken || (session as any)?.idToken || (session as any)?.token || "";
      }

      const symbols = indices.map(idx => idx.symbol);
      
      // 计算需要获取的数据量（从开始日期到现在的天数 + 一些缓冲）
      const startDateObj = new Date(startDate);
      const now = new Date();
      const daysDiff = Math.ceil((now.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24));
      const pageSize = Math.min(Math.max(daysDiff + 50, 100), 1000); // 最少100条，最多1000条

      const data = await getBatchIndexHistoryData(
        symbols,
        token,
        startDate,
        endDate,
        pageSize
      );

      setIndicesQuoteData(data);
    } catch (err) {
      console.error('Failed to load history data:', err);
      setError(true);
    } finally {
      setLoading(false);
    }
  }, [indices]);

  // 清除数据
  const clearData = useCallback(() => {
    setIndicesQuoteData({});
    setError(false);
  }, []);

  return {
    indicesQuoteData,
    loading,
    error,
    loadHistoryData,
    clearData,
  };
}

export default useGainCalculation;
