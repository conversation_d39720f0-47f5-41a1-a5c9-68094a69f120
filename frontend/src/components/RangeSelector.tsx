import React from "react";


type Range = "3m" | "6m" | "1y";

interface RangeSelectorProps {
  selected: Range;
  loading: boolean;
  onChange: (range: Range) => void;
}

/**
 * 区间选择组件，支持禁用与高亮
 */
const RangeSelector: React.FC<RangeSelectorProps> = ({ selected, loading, onChange }) => {
  const ranges: { label: string; value: Range }[] = [
    { label: "最近3个月", value: "3m" },
    { label: "6个月", value: "6m" },
    { label: "最近1年", value: "1y" },
  ];
  return (
    <div className="flex items-center gap-2">
      <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-2 gap-2">
        {ranges.map(r => (
          <button
            key={r.value}
            className={`
              px-4 py-2 text-base font-medium rounded-md transition-all duration-200 outline-none
              ${selected === r.value
                ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-gray-600/50"
              }
              ${loading ? "opacity-60 cursor-not-allowed" : "cursor-pointer"}
              focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-100 dark:focus:ring-offset-gray-700
            `}
            style={{ minWidth: 80, fontSize: "16px" }}
            onClick={() => !loading && onChange(r.value)}
            disabled={loading}
            aria-pressed={selected === r.value}
            aria-label={r.label}
          >
            {r.label}
          </button>
        ))}
      </div>

      {loading && (
        <div className="flex items-center ml-3 text-blue-500">
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span className="text-sm">加载中...</span>
        </div>
      )}
    </div>
  );
};

export default RangeSelector;