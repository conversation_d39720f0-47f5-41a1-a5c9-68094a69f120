import { useEffect, useRef, useState } from "react";
import { apiClient } from "../services/api";
import type { QuoteItem } from "../types";

type Range = "3m" | "6m" | "1y";

interface UseQuoteDataResult {
  data: QuoteItem[];
  loading: boolean;
  error: boolean;
  retry: () => void;
  retryLoading: boolean;
}

export function useQuoteData(symbol: string | null, range: Range): UseQuoteDataResult {
  const [data, setData] = useState<QuoteItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const [retryLoading, setRetryLoading] = useState(false);
  const abortRef = useRef<AbortController | null>(null);

  // 请求函数
  const fetchData = (isRetry = false) => {
    if (!symbol) {
      setData([]);
      setError(false);
      setLoading(false);
      setRetryLoading(false);
      return;
    }
    if (isRetry) {
      setRetryLoading(true);
    } else {
      setLoading(true);
    }
    setError(false);

    // 取消上一次请求
    if (abortRef.current) {
      abortRef.current.abort();
    }
    const controller = new AbortController();
    abortRef.current = controller;

    let pageSize = 90;
    if (range === "6m") pageSize = 180;
    else if (range === "1y") pageSize = 365;

    apiClient.get(`/indices/${symbol}/quotes?page=1&page_size=${pageSize}`, {
      signal: controller.signal,
    })
      .then((res: { data?: any }) => {
        const arr = Array.isArray(res.data?.data) ? res.data.data : (Array.isArray(res.data) ? res.data : []);
        setData(arr);
        setLoading(false);
        setRetryLoading(false);
      })
      .catch((err: unknown) => {
        if (controller.signal.aborted) return;
        setData([]);
        setLoading(false);
        setRetryLoading(false);
        setError(true);
      });
  };

  // 首次和依赖变更自动请求
  useEffect(() => {
    fetchData(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [symbol, range]);

  // retry 方法
  const retry = () => {
    fetchData(true);
  };

  return { data, loading, error, retry, retryLoading };
}

export default useQuoteData;