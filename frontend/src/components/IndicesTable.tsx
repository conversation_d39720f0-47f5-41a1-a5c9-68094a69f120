"use client";
import React from "react";
import type { IndexItem } from "../types";


interface IndicesTableProps {
  indices: IndexItem[];
  activeSymbol: string | null;
  onSelect: (symbol: string) => void;
  sortField?: "latest_close" | "latest_change" | "latest_change_percent";
  sortOrder?: "asc" | "desc";
  onSort?: (field: "latest_close" | "latest_change" | "latest_change_percent") => void;
}

/**
 * 指数表格组件，支持高亮与兜底
 */
const IndicesTable: React.FC<IndicesTableProps> = ({
  indices,
  activeSymbol,
  onSelect,
  sortField,
  sortOrder,
  onSort,
}) => {
  if (!Array.isArray(indices) || indices.length === 0) {
    return <div className="text-gray-400 text-center py-8">暂无指数数据</div>;
  }

  // 排序箭头渲染
  const renderSortArrow = (field: "latest_close" | "latest_change" | "latest_change_percent") => {
    if (sortField !== field) return null;
    return (
      <span className="ml-1 inline-block align-middle">
        {sortOrder === "asc" ? (
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 20 20">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l3-3 3 3" />
          </svg>
        ) : (
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 20 20">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 9l3 3 3-3" />
          </svg>
        )}
      </span>
    );
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-[420px] w-full text-sm">
        <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0">
          <tr>
            <th className="px-4 py-3 text-left text-xs sm:text-xs text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              指数
            </th>
            <th
              className="px-4 py-3 text-right text-xs sm:text-xs text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer select-none touch-manipulation"
              onClick={() => onSort && onSort("latest_close")}
              tabIndex={0}
              style={{ minWidth: 60, fontSize: "15px" }}
            >
              <span className="inline-flex items-center">
                价格
                {renderSortArrow && renderSortArrow("latest_close")}
              </span>
            </th>
            <th
              className="px-4 py-3 text-right text-xs sm:text-xs text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer select-none touch-manipulation"
              onClick={() => onSort && onSort("latest_change_percent")}
              tabIndex={0}
              style={{ minWidth: 80, fontSize: "15px" }}
            >
              <span className="inline-flex items-center">
                涨跌幅
                {renderSortArrow && renderSortArrow("latest_change_percent")}
              </span>
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
          {indices.map((idx) => {
            const isActive = activeSymbol === idx.symbol;
            const isPositive = idx.latest_change >= 0;

            return (
              <tr
                key={idx.symbol}
                className={`
                  cursor-pointer transition-all duration-150
                  ${isActive
                    ? "bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700/50"
                  }
                `}
                onClick={() => onSelect(idx.symbol)}
              >
                <td className="px-4 py-4">
                  <div className="flex flex-col">
                    <div className={`font-medium truncate ${isActive ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-gray-100'} text-base sm:text-sm`}>
                      {idx.name}
                    </div>
                    <div className={`text-xs font-mono ${isActive ? 'text-blue-600 dark:text-blue-300' : 'text-gray-500 dark:text-gray-400'}`}>
                      {idx.symbol}
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4 text-right">
                  <div className="font-medium text-gray-900 dark:text-gray-100 text-base sm:text-sm">
                    {idx.latest_close}
                  </div>
                  <div className={`text-xs ${isPositive ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                    {isPositive ? '+' : ''}{idx.latest_change}
                  </div>
                </td>
                <td className="px-4 py-4 text-right">
                  <span className={`
                    inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                    ${isPositive
                      ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                      : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                    }
                  `} style={{ fontSize: "14px" }}>
                    {isPositive ? '+' : ''}{idx.latest_change_percent}%
                  </span>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default IndicesTable;
