"use client";

import React, { useState, useRef, useEffect } from "react";

interface DatePickerProps {
  value: string;
  onChange: (date: string) => void;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  minDate?: string;
  maxDate?: string;
  className?: string;
}

/**
 * 日期选择器组件，用于选择基准日期计算涨幅
 */
const DatePicker: React.FC<DatePickerProps> = ({
  value,
  onChange,
  label = "选择日期",
  placeholder = "请选择日期",
  disabled = false,
  minDate,
  maxDate,
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [displayValue, setDisplayValue] = useState("");
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 格式化显示日期
  useEffect(() => {
    if (value) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        setDisplayValue(date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        }));
      }
    } else {
      setDisplayValue("");
    }
  }, [value]);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理日期选择
  const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = event.target.value;
    onChange(selectedDate);
    setIsOpen(false);
  };

  // 快捷日期选项
  const quickDates = [
    { label: "今天", value: new Date().toISOString().split('T')[0] },
    { label: "1周前", value: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
    { label: "1个月前", value: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
    { label: "3个月前", value: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
    { label: "6个月前", value: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
    { label: "1年前", value: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
  ];

  return (
    <div className={`relative ${className}`} ref={containerRef}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
        </label>
      )}
      
      <div className="relative">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className={`
            w-full px-4 py-3 text-left bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 
            rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
            transition-all duration-200 flex items-center justify-between
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 dark:hover:border-gray-500 cursor-pointer'}
            ${isOpen ? 'ring-2 ring-blue-500 border-transparent' : ''}
          `}
        >
          <span className={displayValue ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'}>
            {displayValue || placeholder}
          </span>
          <svg 
            className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {isOpen && (
          <div className="absolute z-50 w-full mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg overflow-hidden">
            {/* 快捷选择 */}
            <div className="p-3 border-b border-gray-200 dark:border-gray-600">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">快捷选择</h4>
              <div className="grid grid-cols-2 gap-2">
                {quickDates.map((quick) => (
                  <button
                    key={quick.label}
                    onClick={() => {
                      onChange(quick.value);
                      setIsOpen(false);
                    }}
                    className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors duration-150"
                  >
                    {quick.label}
                  </button>
                ))}
              </div>
            </div>

            {/* 日期输入 */}
            <div className="p-3">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                自定义日期
              </label>
              <input
                ref={inputRef}
                type="date"
                value={value}
                onChange={handleDateChange}
                min={minDate}
                max={maxDate}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* 清除按钮 */}
            {value && (
              <div className="p-3 border-t border-gray-200 dark:border-gray-600">
                <button
                  onClick={() => {
                    onChange("");
                    setIsOpen(false);
                  }}
                  className="w-full px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors duration-150"
                >
                  清除日期
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DatePicker;
