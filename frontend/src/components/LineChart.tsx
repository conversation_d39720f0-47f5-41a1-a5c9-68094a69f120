import React, { useState, useRef, useMemo } from "react";
import type { QuoteItem } from "../types";

interface TooltipState {
  index: number;
  x: number;
  y: number;
  visible: boolean;
  clientX: number;
  clientY: number;
}

/**
 * 增强版折线图组件，支持更丰富的交互和视觉效果
 */
const LineChart: React.FC<{ data: QuoteItem[] }> = React.memo(({ data }) => {
  const [tooltip, setTooltip] = useState<TooltipState | null>(null);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const svgRef = useRef<SVGSVGElement>(null);
  const touchTimeout = useRef<NodeJS.Timeout | null>(null);

  // 数据预处理和统计计算
  const chartData = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0) {
      return null;
    }

    const closes = data.map(q => q.close);
    const min = Math.min(...closes);
    const max = Math.max(...closes);
    const range = max - min || 1;

    // 计算整体涨跌幅
    const firstClose = closes[0];
    const lastClose = closes[closes.length - 1];
    const totalChange = lastClose - firstClose;
    const totalChangePercent = (totalChange / firstClose) * 100;

    // 计算最大回撤
    let maxDrawdown = 0;
    let peak = closes[0];
    for (const close of closes) {
      if (close > peak) {
        peak = close;
      } else {
        const drawdown = ((peak - close) / peak) * 100;
        if (drawdown > maxDrawdown) {
          maxDrawdown = drawdown;
        }
      }
    }

    return {
      closes,
      min,
      max,
      range,
      totalChange,
      totalChangePercent,
      maxDrawdown,
      volatility: range / ((min + max) / 2) * 100 // 简单波动率计算
    };
  }, [data]);

  if (!chartData) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
        暂无数据
      </div>
    );
  }

  // 响应式SVG尺寸
  const viewBoxWidth = 800;
  const viewBoxHeight = 400;
  const padding = 60;

  const { closes, min, max, range, totalChange, totalChangePercent, maxDrawdown, volatility } = chartData;

  // 计算点坐标
  const pointCoords = closes.map((close: number, i: number) => {
    const x = padding + ((viewBoxWidth - 2 * padding) * i) / (closes.length - 1);
    const y = viewBoxHeight - padding - ((viewBoxHeight - 2 * padding) * (close - min)) / range;
    return { x, y };
  });
  const points = pointCoords.map(p => `${p.x},${p.y}`).join(" ");

  // 创建网格线
  const gridLines = [];
  const numGridLines = 5;
  for (let i = 0; i <= numGridLines; i++) {
    const y = padding + ((viewBoxHeight - 2 * padding) * i) / numGridLines;
    gridLines.push(
      <line
        key={`grid-${i}`}
        x1={padding}
        y1={y}
        x2={viewBoxWidth - padding}
        y2={y}
        stroke="currentColor"
        strokeWidth="0.5"
        className="text-gray-300 dark:text-gray-600"
        opacity="0.5"
      />
    );
  }

  // 创建垂直网格线
  const verticalGridLines = [];
  const numVerticalLines = 4;
  for (let i = 1; i < numVerticalLines; i++) {
    const x = padding + ((viewBoxWidth - 2 * padding) * i) / numVerticalLines;
    verticalGridLines.push(
      <line
        key={`vgrid-${i}`}
        x1={x}
        y1={padding}
        x2={x}
        y2={viewBoxHeight - padding}
        stroke="currentColor"
        strokeWidth="0.5"
        className="text-gray-300 dark:text-gray-600"
        opacity="0.3"
      />
    );
  }

  // 计算显示的价格标签
  const priceLabels = [];
  for (let i = 0; i <= numGridLines; i++) {
    const value = max - (range * i) / numGridLines;
    const y = padding + ((viewBoxHeight - 2 * padding) * i) / numGridLines;
    priceLabels.push(
      <text
        key={`label-${i}`}
        x={padding - 10}
        y={y + 4}
        fontSize="12"
        textAnchor="end"
        className="fill-gray-600 dark:fill-gray-400"
      >
        {value.toFixed(2)}
      </text>
    );
  }

  return (
    <div className="w-full h-full flex flex-col">
      {/* 增强的图表统计信息 */}
      <div className="flex flex-col gap-3 mb-4 px-2">
        {/* 第一行：基本信息 */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          <div className="flex items-center gap-3 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">收盘价走势</span>
            </div>
            <span className="text-gray-500 dark:text-gray-400">
              {data.length} 个数据点
            </span>
          </div>
          <div className="flex items-center gap-3 text-sm">
            <span className="text-green-600 dark:text-green-400">
              最低 {min.toFixed(2)}
            </span>
            <span className="text-red-600 dark:text-red-400">
              最高 {max.toFixed(2)}
            </span>
          </div>
        </div>

        {/* 第二行：统计指标 */}
        <div className="flex flex-wrap items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <span className="text-gray-500 dark:text-gray-400">区间涨跌:</span>
            <span className={`font-medium ${totalChangePercent >= 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
              {totalChangePercent >= 0 ? '+' : ''}{totalChangePercent.toFixed(2)}%
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-gray-500 dark:text-gray-400">最大回撤:</span>
            <span className="font-medium text-orange-600 dark:text-orange-400">
              -{maxDrawdown.toFixed(2)}%
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-gray-500 dark:text-gray-400">波动率:</span>
            <span className="font-medium text-purple-600 dark:text-purple-400">
              {volatility.toFixed(2)}%
            </span>
          </div>
        </div>
      </div>

      {/* SVG 图表 */}
      <div className="flex-1 min-h-0 w-full max-w-full overflow-x-auto">
        <div className="w-full" style={{ minWidth: 0 }}>
          <svg
            viewBox={`0 0 ${viewBoxWidth} ${viewBoxHeight}`}
            className="w-full h-[220px] sm:h-[320px] md:h-[400px] max-w-full"
            role="img"
            aria-label="指数走势图"
            ref={svgRef}
            style={{ touchAction: "pan-x pinch-zoom" }}
          >
          {/* 渐变定义 */}
          <defs>
            <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.15" />
              <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.02" />
            </linearGradient>
            <filter id="dropShadow">
              <feDropShadow dx="0" dy="1" stdDeviation="1" floodOpacity="0.3"/>
            </filter>
          </defs>

          {/* 网格线 */}
          {gridLines}
          {verticalGridLines}

          {/* 价格标签 */}
          {priceLabels}

          {/* 渐变填充区域 */}
          <polygon
            fill="url(#areaGradient)"
            points={`${padding},${viewBoxHeight - padding} ${points} ${viewBoxWidth - padding},${viewBoxHeight - padding}`}
          />

          {/* 悬停时的垂直指示线 */}
          {hoveredIndex !== null && (
            <line
              x1={pointCoords[hoveredIndex].x}
              y1={padding}
              x2={pointCoords[hoveredIndex].x}
              y2={viewBoxHeight - padding}
              stroke="#6b7280"
              strokeWidth="1"
              strokeDasharray="4,4"
              opacity="0.7"
            />
          )}

          {/* 主要折线 */}
          <polyline
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2.5"
            points={points}
            className="drop-shadow-sm"
            filter="url(#dropShadow)"
          />

          {/* 数据点（仅在数据点较少时显示） */}
          {closes.length <= 50 && pointCoords.map(({ x, y }, i) => {
            const item = data[i];
            const isHovered = hoveredIndex === i;

            // 鼠标/触摸事件
            const handleMouseEnter = (e: React.MouseEvent) => {
              setHoveredIndex(i);
              setTooltip({
                index: i,
                x,
                y,
                visible: true,
                clientX: e.clientX,
                clientY: e.clientY,
              });
            };
            const handleMouseLeave = () => {
              setHoveredIndex(null);
              setTooltip(null);
            };
            const handleTouchStart = (e: React.TouchEvent) => {
              if (touchTimeout.current) clearTimeout(touchTimeout.current);
              const touch = e.touches[0];
              touchTimeout.current = setTimeout(() => {
                setHoveredIndex(i);
                setTooltip({
                  index: i,
                  x,
                  y,
                  visible: true,
                  clientX: touch.clientX,
                  clientY: touch.clientY,
                });
              }, 300); // 长按 300ms 显示
            };
            const handleTouchEnd = () => {
              if (touchTimeout.current) clearTimeout(touchTimeout.current);
              setTimeout(() => {
                setHoveredIndex(null);
                setTooltip(null);
              }, 200);
            };
            const handleClick = (e: React.MouseEvent) => {
              setHoveredIndex(i);
              setTooltip({
                index: i,
                x,
                y,
                visible: true,
                clientX: e.clientX,
                clientY: e.clientY,
              });
            };

            return (
              <g key={i}>
                {/* 数据点 */}
                <circle
                  cx={x}
                  cy={y}
                  r={isHovered ? "5" : "3"}
                  fill="#3b82f6"
                  className={`transition-all duration-200 cursor-pointer ${
                    isHovered ? 'opacity-100 drop-shadow-md' : 'opacity-70 hover:opacity-100'
                  }`}
                  onMouseEnter={handleMouseEnter}
                  onMouseLeave={handleMouseLeave}
                  onTouchStart={handleTouchStart}
                  onTouchEnd={handleTouchEnd}
                  onClick={handleClick}
                />
                {/* 悬停时的外圈 */}
                {isHovered && (
                  <circle
                    cx={x}
                    cy={y}
                    r="8"
                    fill="none"
                    stroke="#3b82f6"
                    strokeWidth="2"
                    opacity="0.3"
                    className="animate-pulse"
                  />
                )}
              </g>
            );
          })}

          {/* 不可见的交互区域（用于更好的触摸体验） */}
          {pointCoords.map(({ x, y }, i) => (
            <circle
              key={`interaction-${i}`}
              cx={x}
              cy={y}
              r="15"
              fill="transparent"
              className="cursor-pointer"
              onMouseEnter={(e: React.MouseEvent) => {
                setHoveredIndex(i);
                setTooltip({
                  index: i,
                  x,
                  y,
                  visible: true,
                  clientX: e.clientX,
                  clientY: e.clientY,
                });
              }}
              onMouseLeave={() => {
                setHoveredIndex(null);
                setTooltip(null);
              }}
            />
          ))}
        </svg>
        {/* 增强的Tooltip渲染 */}
        {tooltip && tooltip.visible && data[tooltip.index] && (() => {
          const item = data[tooltip.index];
          const prev = tooltip.index > 0 ? data[tooltip.index - 1] : undefined;
          const change = prev ? (item.close - prev.close) : 0;
          const changePercent = prev ? ((change / prev.close) * 100) : 0;

          // 计算从起始点的累计涨跌幅
          const firstClose = data[0].close;
          const cumulativeChange = item.close - firstClose;
          const cumulativeChangePercent = (cumulativeChange / firstClose) * 100;

          // 移动端防止 tooltip 溢出屏幕
          let left = tooltip.clientX + 12;
          let top = tooltip.clientY - 12;
          if (typeof window !== "undefined") {
            const tooltipWidth = 200;
            const tooltipHeight = 120;
            if (left + tooltipWidth > window.innerWidth) {
              left = window.innerWidth - tooltipWidth - 8;
            }
            if (top + tooltipHeight > window.innerHeight) {
              top = window.innerHeight - tooltipHeight - 8;
            }
            if (top < 0) top = 8;
          }

          return (
            <div
              style={{
                position: "fixed",
                left,
                top,
                zIndex: 1000,
                pointerEvents: "none",
                minWidth: 180,
                maxWidth: "90vw",
                fontSize: "14px",
                wordBreak: "break-all"
              }}
              className={`
                px-4 py-3 rounded-lg shadow-xl border
                bg-white dark:bg-gray-800
                text-gray-800 dark:text-gray-100
                border-gray-200 dark:border-gray-600
                text-sm
                transition-all duration-200
                select-none
                backdrop-blur-sm
              `}
            >
              <div className="font-bold mb-2 text-blue-600 dark:text-blue-400">{item.date}</div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">收盘价:</span>
                  <span className="font-mono font-medium">{item.close?.toFixed?.(2) ?? "--"}</span>
                </div>
                {prev && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">日涨跌:</span>
                    <span className={`font-medium ${changePercent >= 0 ? "text-red-500 dark:text-red-400" : "text-green-500 dark:text-green-400"}`}>
                      {changePercent >= 0 ? "+" : ""}{isFinite(changePercent) ? changePercent.toFixed(2) : "--"}%
                    </span>
                  </div>
                )}
                <div className="flex justify-between border-t border-gray-200 dark:border-gray-600 pt-1">
                  <span className="text-gray-600 dark:text-gray-400">累计涨跌:</span>
                  <span className={`font-medium ${cumulativeChangePercent >= 0 ? "text-red-500 dark:text-red-400" : "text-green-500 dark:text-green-400"}`}>
                    {cumulativeChangePercent >= 0 ? "+" : ""}{isFinite(cumulativeChangePercent) ? cumulativeChangePercent.toFixed(2) : "--"}%
                  </span>
                </div>
              </div>
            </div>
          );
        })()}
        </div>
      </div>
    </div>
  );
});
LineChart.displayName = "LineChart";

export default LineChart;