import React, { useState, useRef } from "react";
import type { QuoteItem } from "../types";


/**
 * 折线图组件，响应式设计，数据校验与兜底，性能优化（memo）
 */
const LineChart: React.FC<{ data: QuoteItem[] }> = React.memo(({ data }) => {
  const [tooltip, setTooltip] = useState<{
    index: number;
    x: number;
    y: number;
    visible: boolean;
    clientX: number;
    clientY: number;
  } | null>(null);

  const svgRef = useRef<SVGSVGElement>(null);
  const touchTimeout = useRef<NodeJS.Timeout | null>(null);

  if (!Array.isArray(data) || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
        暂无数据
      </div>
    );
  }

  const closes = data.map(q => q.close);
  const min = Math.min(...closes);
  const max = Math.max(...closes);
  const range = max - min || 1;

  // 响应式SVG尺寸
  const viewBoxWidth = 800;
  const viewBoxHeight = 400;
  const padding = 60;

  // 计算点坐标
  const pointCoords = closes.map((close, i) => {
    const x = padding + ((viewBoxWidth - 2 * padding) * i) / (closes.length - 1);
    const y = viewBoxHeight - padding - ((viewBoxHeight - 2 * padding) * (close - min)) / range;
    return { x, y };
  });
  const points = pointCoords.map(p => `${p.x},${p.y}`).join(" ");

  // 创建网格线
  const gridLines = [];
  const numGridLines = 5;
  for (let i = 0; i <= numGridLines; i++) {
    const y = padding + ((viewBoxHeight - 2 * padding) * i) / numGridLines;
    gridLines.push(
      <line
        key={`grid-${i}`}
        x1={padding}
        y1={y}
        x2={viewBoxWidth - padding}
        y2={y}
        stroke="currentColor"
        strokeWidth="0.5"
        className="text-gray-300 dark:text-gray-600"
        opacity="0.5"
      />
    );
  }

  // 计算显示的价格标签
  const priceLabels = [];
  for (let i = 0; i <= numGridLines; i++) {
    const value = max - (range * i) / numGridLines;
    const y = padding + ((viewBoxHeight - 2 * padding) * i) / numGridLines;
    priceLabels.push(
      <text
        key={`label-${i}`}
        x={padding - 10}
        y={y + 4}
        fontSize="12"
        textAnchor="end"
        className="fill-gray-600 dark:fill-gray-400"
      >
        {value.toFixed(2)}
      </text>
    );
  }

  return (
    <div className="w-full h-full flex flex-col">
      {/* 图表统计信息 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 px-2 gap-2">
        <div className="flex items-center gap-3 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-gray-600 dark:text-gray-400">收盘价走势</span>
          </div>
          <span className="text-gray-500 dark:text-gray-400">
            {data.length} 个数据点
          </span>
        </div>
        <div className="flex items-center gap-3 text-sm">
          <span className="text-green-600 dark:text-green-400">
            最低 {min.toFixed(2)}
          </span>
          <span className="text-red-600 dark:text-red-400">
            最高 {max.toFixed(2)}
          </span>
        </div>
      </div>

      {/* SVG 图表 */}
      <div className="flex-1 min-h-0 w-full max-w-full overflow-x-auto">
        <div className="w-full" style={{ minWidth: 0 }}>
          <svg
            viewBox={`0 0 ${viewBoxWidth} ${viewBoxHeight}`}
            className="w-full h-[220px] sm:h-[320px] md:h-[400px] max-w-full"
            role="img"
            aria-label="指数走势图"
            ref={svgRef}
            style={{ touchAction: "pan-x pinch-zoom" }}
          >
          {/* 网格线 */}
          {gridLines}

          {/* 价格标签 */}
          {priceLabels}

          {/* 渐变填充区域 */}
          <defs>
            <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.15" />
              <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.02" />
            </linearGradient>
          </defs>
          <polygon
            fill="url(#areaGradient)"
            points={`${padding},${viewBoxHeight - padding} ${points} ${viewBoxWidth - padding},${viewBoxHeight - padding}`}
          />

          {/* 主要折线 */}
          <polyline
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2.5"
            points={points}
            className="drop-shadow-sm"
          />

          {/* 数据点（仅在数据点较少时显示） */}
          {closes.length <= 50 && pointCoords.map(({ x, y }, i) => {
            const item = data[i];
            // 涨跌幅
            const prevClose = i > 0 ? data[i - 1].close : null;
            const change = prevClose !== null ? item.close - prevClose : 0;
            const changePercent = prevClose !== null ? (change / prevClose) * 100 : 0;

            // 鼠标/触摸事件
            const handleMouseEnter = (e: React.MouseEvent) => {
              setTooltip({
                index: i,
                x,
                y,
                visible: true,
                clientX: e.clientX,
                clientY: e.clientY,
              });
            };
            const handleMouseLeave = () => {
              setTooltip(null);
            };
            const handleTouchStart = (e: React.TouchEvent) => {
              if (touchTimeout.current) clearTimeout(touchTimeout.current);
              const touch = e.touches[0];
              touchTimeout.current = setTimeout(() => {
                setTooltip({
                  index: i,
                  x,
                  y,
                  visible: true,
                  clientX: touch.clientX,
                  clientY: touch.clientY,
                });
              }, 300); // 长按 300ms 显示
            };
            const handleTouchEnd = () => {
              if (touchTimeout.current) clearTimeout(touchTimeout.current);
              setTimeout(() => setTooltip(null), 200);
            };
            const handleClick = (e: React.MouseEvent) => {
              setTooltip({
                index: i,
                x,
                y,
                visible: true,
                clientX: e.clientX,
                clientY: e.clientY,
              });
            };

            return (
              <circle
                key={i}
                cx={x}
                cy={y}
                r="3"
                fill="#3b82f6"
                className="opacity-70 hover:opacity-100 transition-opacity duration-200 cursor-pointer"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
                onTouchStart={handleTouchStart}
                onTouchEnd={handleTouchEnd}
                onClick={handleClick}
              />
            );
          })}
        </svg>
        {/* Tooltip 渲染 */}
        {tooltip && tooltip.visible && data[tooltip.index] && (() => {
          const item = data[tooltip.index];
          const prev = tooltip.index > 0 ? data[tooltip.index - 1] : undefined;
          const change = prev ? (item.close - prev.close) : 0;
          const changePercent = prev ? ((change / prev.close) * 100) : 0;
          // 移动端防止 tooltip 溢出屏幕
          let left = tooltip.clientX + 12;
          let top = tooltip.clientY - 12;
          if (typeof window !== "undefined") {
            const tooltipWidth = 180;
            const tooltipHeight = 90;
            if (left + tooltipWidth > window.innerWidth) {
              left = window.innerWidth - tooltipWidth - 8;
            }
            if (top + tooltipHeight > window.innerHeight) {
              top = window.innerHeight - tooltipHeight - 8;
            }
            if (top < 0) top = 8;
          }
          return (
            <div
              style={{
                position: "fixed",
                left,
                top,
                zIndex: 1000,
                pointerEvents: "none",
                minWidth: 160,
                maxWidth: "90vw",
                fontSize: "15px",
                wordBreak: "break-all"
              }}
              className={`
                px-3 py-2 rounded shadow-lg border
                bg-white dark:bg-gray-900
                text-gray-800 dark:text-gray-100
                border-gray-200 dark:border-gray-700
                text-xs
                transition-all
                select-none
              `}
            >
              <div className="font-bold mb-1">{item.date}</div>
              <div>收盘价：<span className="font-mono">{item.close?.toFixed?.(2) ?? "--"}</span></div>
              {prev && (
                <>
                  <div>
                    涨跌幅：
                    <span className={changePercent >= 0 ? "text-red-500 dark:text-red-400" : "text-green-500 dark:text-green-400"}>
                      {changePercent >= 0 ? "+" : ""}{isFinite(changePercent) ? changePercent.toFixed(2) : "--"}%
                    </span>
                  </div>
                  <div>
                    涨跌额：
                    <span className={change >= 0 ? "text-red-500 dark:text-red-400" : "text-green-500 dark:text-green-400"}>
                      {change >= 0 ? "+" : ""}{isFinite(change) ? change.toFixed(2) : "--"}
                    </span>
                  </div>
                </>
              )}
            </div>
          );
        })()}
        </div>
      </div>
    </div>
  );
});
LineChart.displayName = "LineChart";

export default LineChart;