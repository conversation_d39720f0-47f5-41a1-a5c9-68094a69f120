"use client";

import React, { useState, useMemo } from "react";
import type { IndexItem, QuoteItem } from "../types";
import { calculateGains, formatGain, formatPrice, getGainColorClass, calculateAnnualizedReturn } from "../utils/calculations";
import DatePicker from "./DatePicker";

interface GainCalculatorProps {
  indices: IndexItem[];
  indicesQuoteData: { [symbol: string]: QuoteItem[] };
  onLoadHistoryData?: (startDate: string, endDate?: string) => Promise<void>;
  loading?: boolean;
  className?: string;
}

/**
 * 涨幅计算器组件
 */
const GainCalculator: React.FC<GainCalculatorProps> = ({
  indices,
  indicesQuoteData,
  onLoadHistoryData,
  loading = false,
  className = "",
}) => {
  const [baseDate, setBaseDate] = useState<string>("");
  const [isExpanded, setIsExpanded] = useState(false);

  // 当基准日期改变时，自动加载历史数据
  const handleDateChange = async (newDate: string) => {
    setBaseDate(newDate);
    if (newDate && onLoadHistoryData) {
      await onLoadHistoryData(newDate);
    }
  };

  // 计算所有指数的涨幅
  const gainResults = useMemo(() => {
    if (!baseDate) return {};
    
    const results: { [symbol: string]: any } = {};
    
    for (const index of indices) {
      const quoteData = indicesQuoteData[index.symbol];
      if (quoteData && quoteData.length > 0) {
        const gainResult = calculateGains(quoteData, baseDate);
        if (gainResult) {
          results[index.symbol] = {
            ...gainResult,
            name: index.name,
            symbol: index.symbol,
            annualizedReturn: calculateAnnualizedReturn(gainResult.totalGainPercent, gainResult.tradingDays)
          };
        }
      }
    }
    
    return results;
  }, [baseDate, indices, indicesQuoteData]);

  // 排序结果
  const sortedResults = useMemo(() => {
    return Object.values(gainResults).sort((a: any, b: any) => b.totalGainPercent - a.totalGainPercent);
  }, [gainResults]);

  // 获取默认日期（3个月前）
  const getDefaultDate = () => {
    const date = new Date();
    date.setMonth(date.getMonth() - 3);
    return date.toISOString().split('T')[0];
  };

  return (
    <div className={`bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 overflow-hidden ${className}`}>
      {/* 头部 */}
      <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-gray-800 dark:to-gray-700">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              涨幅计算器
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              选择基准日期，计算各指数的涨幅表现
            </p>
          </div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-purple-600 dark:text-purple-400 hover:bg-purple-100 dark:hover:bg-purple-900/20 rounded-lg transition-colors duration-200"
          >
            {isExpanded ? '收起' : '展开'}
            <svg 
              className={`w-4 h-4 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>

      {/* 内容区域 */}
      {isExpanded && (
        <div className="p-4 sm:p-6">
          {/* 日期选择 */}
          <div className="mb-6">
            <DatePicker
              value={baseDate}
              onChange={handleDateChange}
              label="基准日期"
              placeholder="选择基准日期开始计算"
              maxDate={new Date().toISOString().split('T')[0]}
              disabled={loading}
              className="max-w-md"
            />
            {!baseDate && (
              <div className="mt-3 flex gap-2">
                <button
                  onClick={() => handleDateChange(getDefaultDate())}
                  disabled={loading}
                  className="px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-md transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  使用3个月前
                </button>
              </div>
            )}
            {loading && (
              <div className="mt-3 flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span>正在加载历史数据...</span>
              </div>
            )}
          </div>

          {/* 计算结果 */}
          {baseDate && sortedResults.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                  涨幅排行榜
                </h4>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  基准日期: {new Date(baseDate).toLocaleDateString('zh-CN')}
                </div>
              </div>

              {/* 结果表格 */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        排名
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        指数
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        基准价格
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        当前价格
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        总涨幅
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        从最低点涨幅
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        年化收益率
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                    {sortedResults.map((result: any, index: number) => (
                      <tr key={result.symbol} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <span className={`
                              inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium
                              ${index === 0 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                                index === 1 ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                                index === 2 ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400' :
                                'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'}
                            `}>
                              {index + 1}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {result.name}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                              {result.symbol}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-right text-sm text-gray-900 dark:text-gray-100">
                          {formatPrice(result.basePrice)}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-right text-sm text-gray-900 dark:text-gray-100">
                          {formatPrice(result.currentPrice)}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-right">
                          <div className="text-sm font-medium">
                            <span className={getGainColorClass(result.totalGainPercent)}>
                              {formatGain(result.totalGainPercent, true)}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {formatGain(result.totalGain)}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-right">
                          <div className="text-sm font-medium">
                            <span className={getGainColorClass(result.gainFromLowestPercent)}>
                              {formatGain(result.gainFromLowestPercent, true)}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            最低: {formatPrice(result.lowestPrice)}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-right">
                          <span className={`text-sm font-medium ${getGainColorClass(result.annualizedReturn)}`}>
                            {formatGain(result.annualizedReturn, true)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 统计摘要 */}
              <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <div className="text-sm text-gray-600 dark:text-gray-400">最佳表现</div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {sortedResults[0]?.name}
                  </div>
                  <div className={`text-sm font-medium ${getGainColorClass(sortedResults[0]?.totalGainPercent)}`}>
                    {formatGain(sortedResults[0]?.totalGainPercent, true)}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <div className="text-sm text-gray-600 dark:text-gray-400">平均涨幅</div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {(() => {
                      const avg = sortedResults.reduce((sum: number, r: any) => sum + r.totalGainPercent, 0) / sortedResults.length;
                      return formatGain(avg, true);
                    })()}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <div className="text-sm text-gray-600 dark:text-gray-400">上涨指数</div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {sortedResults.filter((r: any) => r.totalGainPercent > 0).length} / {sortedResults.length}
                  </div>
                </div>
              </div>
            </div>
          )}

          {baseDate && sortedResults.length === 0 && (
            <div className="text-center py-8">
              <div className="text-gray-500 dark:text-gray-400">
                暂无可计算的数据，请选择其他日期
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default GainCalculator;
