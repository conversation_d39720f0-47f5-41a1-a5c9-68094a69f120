import { redirect } from "next/navigation";
import IndexDetailClient from "../../../components/IndexDetailClient";
import { getIndexQuotes, getIndexLatest } from "../../../services/api";
import { getAuthSession } from "../../../utils/auth";

interface QuoteItem {
  date: string;
  close: number;
  open: number;
  high: number;
  low: number;
  volume: number;
}


export default async function IndexDetailPage({ params }: { params: { symbol: string } }) {
  const { session, token } = await getAuthSession();

  if (!session) {
    redirect("/auth/signin");
  }

  const symbol = params.symbol;
  const quotesData = await getIndexQuotes(symbol, token);
  const latest = await getIndexLatest(symbol, token);

  return (
    <IndexDetailClient
      symbol={symbol}
      quotes={quotesData.items || []}
      latest={latest}
      total={quotesData.total || 0}
      pageSize={20}
      session={session}
    />
  );
}