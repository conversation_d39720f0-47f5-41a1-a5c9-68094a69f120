import { redirect } from "next/navigation";
import IndicesClient from "../../components/IndicesClient";
import { getIndicesList } from "../../services/api";
import { getAuthSession } from "../../utils/auth";

interface IndexItem {
  symbol: string;
  name: string;
  latest_close: number;
  latest_change: number;
  latest_change_percent: number;
}


export default async function IndicesPage() {
  const { session, token } = await getAuthSession();

  // 生产环境下 session 为空时跳转登录页
  if (process.env.NODE_ENV !== "development" && !session) {
    redirect("/auth/signin");
  }

  const indices = await getIndicesList(token);

  return <IndicesClient indices={indices} session={session} />;
}