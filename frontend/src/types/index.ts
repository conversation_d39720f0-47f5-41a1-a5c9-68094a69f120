// 指数行情相关类型
export interface IndexItem {
  symbol: string;
  name: string;
  latest_close: number;
  latest_change: number;
  latest_change_percent: number;
}

export interface IndexDetail {
  symbol: string;
  name: string;
  price: number;
  open: number;
  high: number;
  low: number;
  prevClose: number;
  change: number;
  percent: number;
  volume: number;
  turnover: number;
  time: string;
}

// API 错误响应类型
export interface ApiErrorResponse {
  error: string;
  status: string;
}

// 行情数据类型
export interface QuoteItem {
  date: string;
  close: number;
}