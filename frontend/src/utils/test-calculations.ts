/**
 * 测试涨幅计算功能的工具函数
 */

import { calculateGains, formatGain, formatPrice, getGainColorClass } from './calculations';
import type { QuoteItem } from '../types';

// 模拟测试数据
const mockQuoteData: QuoteItem[] = [
  { date: '2024-01-01', close: 3000 },
  { date: '2024-01-02', close: 2950 },
  { date: '2024-01-03', close: 2900 }, // 最低点
  { date: '2024-01-04', close: 3100 },
  { date: '2024-01-05', close: 3200 }, // 最高点
  { date: '2024-01-06', close: 3150 },
  { date: '2024-01-07', close: 3180 }, // 当前价格
];

/**
 * 测试涨幅计算功能
 */
export function testGainCalculations() {
  console.log('=== 测试涨幅计算功能 ===');
  
  // 测试从基准日期计算涨幅
  const baseDate = '2024-01-01';
  const result = calculateGains(mockQuoteData, baseDate);
  
  if (!result) {
    console.error('❌ 涨幅计算失败');
    return false;
  }
  
  console.log('✅ 基本涨幅计算测试通过');
  console.log(`基准日期: ${result.baseDate}`);
  console.log(`基准价格: ${formatPrice(result.basePrice)}`);
  console.log(`当前价格: ${formatPrice(result.currentPrice)}`);
  console.log(`总涨幅: ${formatGain(result.totalGainPercent, true)}`);
  console.log(`最低点: ${formatPrice(result.lowestPrice)} (${result.lowestDate})`);
  console.log(`从最低点涨幅: ${formatGain(result.gainFromLowestPercent, true)}`);
  console.log(`最高点: ${formatPrice(result.highestPrice)} (${result.highestDate})`);
  console.log(`交易天数: ${result.tradingDays}`);
  console.log(`波动率: ${result.volatility.toFixed(2)}%`);
  
  // 验证计算结果
  const expectedTotalGain = 3180 - 3000; // 180
  const expectedTotalGainPercent = (180 / 3000) * 100; // 6%
  const expectedGainFromLowest = 3180 - 2900; // 280
  const expectedGainFromLowestPercent = (280 / 2900) * 100; // ~9.66%
  
  if (Math.abs(result.totalGain - expectedTotalGain) > 0.01) {
    console.error(`❌ 总涨幅计算错误: 期望 ${expectedTotalGain}, 实际 ${result.totalGain}`);
    return false;
  }
  
  if (Math.abs(result.totalGainPercent - expectedTotalGainPercent) > 0.01) {
    console.error(`❌ 总涨幅百分比计算错误: 期望 ${expectedTotalGainPercent.toFixed(2)}%, 实际 ${result.totalGainPercent.toFixed(2)}%`);
    return false;
  }
  
  if (Math.abs(result.gainFromLowestPercent - expectedGainFromLowestPercent) > 0.01) {
    console.error(`❌ 从最低点涨幅计算错误: 期望 ${expectedGainFromLowestPercent.toFixed(2)}%, 实际 ${result.gainFromLowestPercent.toFixed(2)}%`);
    return false;
  }
  
  console.log('✅ 所有涨幅计算验证通过');
  return true;
}

/**
 * 测试格式化函数
 */
export function testFormatFunctions() {
  console.log('\n=== 测试格式化函数 ===');
  
  // 测试价格格式化
  const price = 3456.789;
  const formattedPrice = formatPrice(price);
  if (formattedPrice !== '3456.79') {
    console.error(`❌ 价格格式化错误: 期望 '3456.79', 实际 '${formattedPrice}'`);
    return false;
  }
  console.log('✅ 价格格式化测试通过');
  
  // 测试涨幅格式化
  const positiveGain = 5.67;
  const negativeGain = -3.45;
  const zeroGain = 0;
  
  if (formatGain(positiveGain, true) !== '+5.67%') {
    console.error(`❌ 正涨幅格式化错误`);
    return false;
  }
  
  if (formatGain(negativeGain, true) !== '-3.45%') {
    console.error(`❌ 负涨幅格式化错误`);
    return false;
  }
  
  if (formatGain(zeroGain, true) !== '0.00%') {
    console.error(`❌ 零涨幅格式化错误`);
    return false;
  }
  
  console.log('✅ 涨幅格式化测试通过');
  
  // 测试颜色类名
  const positiveColor = getGainColorClass(5.67);
  const negativeColor = getGainColorClass(-3.45);
  const zeroColor = getGainColorClass(0);
  
  if (!positiveColor.includes('red')) {
    console.error(`❌ 正涨幅颜色类名错误: ${positiveColor}`);
    return false;
  }
  
  if (!negativeColor.includes('green')) {
    console.error(`❌ 负涨幅颜色类名错误: ${negativeColor}`);
    return false;
  }
  
  console.log('✅ 颜色类名测试通过');
  return true;
}

/**
 * 测试边界情况
 */
export function testEdgeCases() {
  console.log('\n=== 测试边界情况 ===');
  
  // 测试空数据
  const emptyResult = calculateGains([], '2024-01-01');
  if (emptyResult !== null) {
    console.error('❌ 空数据应该返回 null');
    return false;
  }
  console.log('✅ 空数据测试通过');
  
  // 测试单条数据
  const singleData: QuoteItem[] = [{ date: '2024-01-01', close: 3000 }];
  const singleResult = calculateGains(singleData, '2024-01-01');
  if (!singleResult || singleResult.totalGainPercent !== 0) {
    console.error('❌ 单条数据测试失败');
    return false;
  }
  console.log('✅ 单条数据测试通过');
  
  // 测试不存在的基准日期
  const nonExistentDateResult = calculateGains(mockQuoteData, '2023-12-31');
  if (!nonExistentDateResult) {
    console.error('❌ 不存在的基准日期应该使用最接近的日期');
    return false;
  }
  console.log('✅ 不存在基准日期测试通过');
  
  return true;
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log('🚀 开始运行涨幅计算功能测试...\n');
  
  const tests = [
    testGainCalculations,
    testFormatFunctions,
    testEdgeCases,
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      if (test()) {
        passedTests++;
      }
    } catch (error) {
      console.error(`❌ 测试执行出错:`, error);
    }
  }
  
  console.log(`\n📊 测试结果: ${passedTests}/${tests.length} 通过`);
  
  if (passedTests === tests.length) {
    console.log('🎉 所有测试通过！涨幅计算功能正常工作。');
    return true;
  } else {
    console.log('⚠️  部分测试失败，请检查代码。');
    return false;
  }
}

// 如果在浏览器环境中，可以通过控制台运行测试
if (typeof window !== 'undefined') {
  (window as any).testGainCalculations = runAllTests;
}
