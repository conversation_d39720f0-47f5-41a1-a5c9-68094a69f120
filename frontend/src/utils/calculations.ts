import type { QuoteItem } from "../types";

/**
 * 涨幅计算相关的工具函数
 */

export interface GainCalculationResult {
  // 基准日期相关
  baseDate: string;
  basePrice: number;
  currentPrice: number;
  
  // 涨幅计算
  totalGain: number;
  totalGainPercent: number;
  
  // 最低点相关
  lowestDate: string;
  lowestPrice: number;
  gainFromLowest: number;
  gainFromLowestPercent: number;
  
  // 最高点相关
  highestDate: string;
  highestPrice: number;
  maxDrawdownFromHigh: number;
  maxDrawdownFromHighPercent: number;
  
  // 统计信息
  tradingDays: number;
  volatility: number;
  averagePrice: number;
}

/**
 * 计算从指定日期到当前的涨幅，包括从最低点的涨幅
 */
export function calculateGains(
  data: QuoteItem[],
  baseDate: string
): GainCalculationResult | null {
  if (!data || data.length === 0) {
    return null;
  }

  // 按日期排序确保数据顺序正确
  const sortedData = [...data].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  
  // 找到基准日期的索引
  const baseDateIndex = sortedData.findIndex(item => item.date === baseDate);
  if (baseDateIndex === -1) {
    // 如果找不到确切日期，找最接近的日期
    const targetDate = new Date(baseDate);
    let closestIndex = 0;
    let minDiff = Math.abs(new Date(sortedData[0].date).getTime() - targetDate.getTime());
    
    for (let i = 1; i < sortedData.length; i++) {
      const diff = Math.abs(new Date(sortedData[i].date).getTime() - targetDate.getTime());
      if (diff < minDiff) {
        minDiff = diff;
        closestIndex = i;
      }
    }
    
    if (closestIndex === -1) {
      return null;
    }
  }

  const actualBaseIndex = baseDateIndex !== -1 ? baseDateIndex : 0;
  const baseItem = sortedData[actualBaseIndex];
  const currentItem = sortedData[sortedData.length - 1];
  
  // 获取基准日期之后的数据
  const dataFromBase = sortedData.slice(actualBaseIndex);
  
  // 计算基本涨幅
  const totalGain = currentItem.close - baseItem.close;
  const totalGainPercent = (totalGain / baseItem.close) * 100;
  
  // 找到最低点和最高点
  let lowestItem = dataFromBase[0];
  let highestItem = dataFromBase[0];
  
  for (const item of dataFromBase) {
    if (item.close < lowestItem.close) {
      lowestItem = item;
    }
    if (item.close > highestItem.close) {
      highestItem = item;
    }
  }
  
  // 计算从最低点的涨幅
  const gainFromLowest = currentItem.close - lowestItem.close;
  const gainFromLowestPercent = (gainFromLowest / lowestItem.close) * 100;
  
  // 计算从最高点的回撤
  const maxDrawdownFromHigh = currentItem.close - highestItem.close;
  const maxDrawdownFromHighPercent = (maxDrawdownFromHigh / highestItem.close) * 100;
  
  // 计算统计信息
  const prices = dataFromBase.map(item => item.close);
  const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
  
  // 简单波动率计算（标准差）
  const variance = prices.reduce((sum, price) => sum + Math.pow(price - averagePrice, 2), 0) / prices.length;
  const volatility = Math.sqrt(variance) / averagePrice * 100;
  
  return {
    baseDate: baseItem.date,
    basePrice: baseItem.close,
    currentPrice: currentItem.close,
    
    totalGain,
    totalGainPercent,
    
    lowestDate: lowestItem.date,
    lowestPrice: lowestItem.close,
    gainFromLowest,
    gainFromLowestPercent,
    
    highestDate: highestItem.date,
    highestPrice: highestItem.close,
    maxDrawdownFromHigh,
    maxDrawdownFromHighPercent,
    
    tradingDays: dataFromBase.length,
    volatility,
    averagePrice,
  };
}

/**
 * 批量计算多个指数的涨幅
 */
export function calculateBatchGains(
  indicesData: { [symbol: string]: QuoteItem[] },
  baseDate: string
): { [symbol: string]: GainCalculationResult | null } {
  const results: { [symbol: string]: GainCalculationResult | null } = {};
  
  for (const [symbol, data] of Object.entries(indicesData)) {
    results[symbol] = calculateGains(data, baseDate);
  }
  
  return results;
}

/**
 * 格式化涨幅显示
 */
export function formatGain(gain: number, isPercent: boolean = false): string {
  const prefix = gain >= 0 ? '+' : '';
  const suffix = isPercent ? '%' : '';
  return `${prefix}${gain.toFixed(2)}${suffix}`;
}

/**
 * 格式化价格显示
 */
export function formatPrice(price: number): string {
  return price.toFixed(2);
}

/**
 * 获取涨跌颜色类名
 */
export function getGainColorClass(gain: number, isDark: boolean = false): string {
  if (gain > 0) {
    return isDark ? 'text-red-400' : 'text-red-600';
  } else if (gain < 0) {
    return isDark ? 'text-green-400' : 'text-green-600';
  } else {
    return isDark ? 'text-gray-400' : 'text-gray-600';
  }
}

/**
 * 获取涨跌背景色类名
 */
export function getGainBgColorClass(gain: number, isDark: boolean = false): string {
  if (gain > 0) {
    return isDark ? 'bg-red-900/20' : 'bg-red-100';
  } else if (gain < 0) {
    return isDark ? 'bg-green-900/20' : 'bg-green-100';
  } else {
    return isDark ? 'bg-gray-700/20' : 'bg-gray-100';
  }
}

/**
 * 计算年化收益率
 */
export function calculateAnnualizedReturn(
  totalGainPercent: number,
  tradingDays: number
): number {
  if (tradingDays <= 0) return 0;
  
  // 假设一年有252个交易日
  const years = tradingDays / 252;
  if (years <= 0) return 0;
  
  // 年化收益率 = (1 + 总收益率)^(1/年数) - 1
  const annualizedReturn = Math.pow(1 + totalGainPercent / 100, 1 / years) - 1;
  return annualizedReturn * 100;
}

/**
 * 计算夏普比率（简化版本，假设无风险利率为0）
 */
export function calculateSharpeRatio(
  annualizedReturn: number,
  volatility: number
): number {
  if (volatility <= 0) return 0;
  return annualizedReturn / volatility;
}
