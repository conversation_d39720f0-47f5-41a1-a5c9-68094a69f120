import { getServerSession } from "next-auth";
import { authOptions } from "../app/api/auth/[...nextauth]/route";

/**
 * 获取认证 session 和 token
 * - 开发环境返回 mock session 和 token
 * - 生产环境正常获取 session 并提取 token
 */
export async function getAuthSession() {
  let session = await getServerSession(authOptions);
  let token = "";

  // 开发环境下返回 mock
  if (process.env.NODE_ENV === "development") {
    session = { user: { id: "dev", name: "dev" }, expires: "2099-12-31T23:59:59.999Z" };
    token = "dev-token";
  } else {
    if (!session) {
      // 生产环境下未登录，session 为 null，token 为空
      token = "";
    } else {
      token = (session as any).token || "";
    }
  }

  return { session, token };
}